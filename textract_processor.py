import json
import time
import boto3
import os
import logging
from typing import <PERSON><PERSON>, Dict, Optional
from botocore.exceptions import ClientError


def process_file_with_textract(
    file_path: str,
    region: str = 'us-east-1',
    bucket_name: str = None,
    temp_prefix: str = 'temp-textract',
    use_sync: bool = True,
    logger: Optional[logging.Logger] = None
) -> Tuple[Dict, str]:
    """
    Process a local file with AWS Textract and return both the raw response and formatted text.
    
    Args:
        file_path (str): Local path to the file to process
        region (str): AWS region for Textract service
        bucket_name (str): S3 bucket name for temporary file upload
        temp_prefix (str): S3 prefix for temporary files
        use_sync (bool): Whether to prefer synchronous processing
        logger (Optional[logging.Logger]): Logger instance
        
    Returns:
        Tuple[Dict, str]: (textract_response, formatted_text_with_coordinates)
        
    Example formatted text output:
        === TEXT WITH COORDINATES ===
        text, x1, y1, x2, y2
        SmartWay, 0.4509, 0.0311, 0.5840, 0.0538
        TRANSPORTATION, 0.4522, 0.0514, 0.6103, 0.0654
        ...
    """
    if logger is None:
        logger = logging.getLogger(__name__)
    
    # Initialize AWS clients
    textract_client = boto3.client('textract', region_name=region)
    s3_client = boto3.client('s3', region_name=region)
    
    if bucket_name is None:
        raise ValueError("bucket_name is required for S3 upload")
    
    try:
        # Step 1: Upload file to S3
        file_name = os.path.basename(file_path)
        s3_key = f"{temp_prefix}/{file_name}"
        logger.info(f"Uploading {file_path} to s3://{bucket_name}/{s3_key}")
        
        s3_client.upload_file(file_path, bucket_name, s3_key)
        s3_uri = f"s3://{bucket_name}/{s3_key}"
        logger.info(f"Successfully uploaded to {s3_uri}")
        
        # Step 2: Invoke Textract
        textract_response = _invoke_textract(textract_client, s3_uri, use_sync, logger)
        
        # Step 3: Get final results
        textract_results = _check_textract_job_status(textract_client, textract_response, logger)
        
        # Step 4: Convert to formatted text
        formatted_text = _convert_textract_to_structured_format(textract_results, logger)
        
        # Step 5: Clean up S3 file (optional)
        try:
            s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
            logger.info(f"Cleaned up temporary S3 file: {s3_uri}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to clean up S3 file: {cleanup_error}")
        
        return textract_results, formatted_text
        
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        raise


def _invoke_textract(textract_client, s3_uri: str, use_sync: bool, logger) -> dict:
    """Invoke Textract to extract text from document."""
    try:
        # Parse S3 URI to get bucket and key
        s3_parts = s3_uri.replace('s3://', '').split('/', 1)
        bucket = s3_parts[0]
        key = s3_parts[1]
        
        logger.info(f"Starting Textract text extraction for {s3_uri}")
        
        # Always try synchronous detection first for faster processing
        try:
            logger.info("Attempting synchronous Textract text detection")
            response = textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            logger.info("Synchronous Textract text detection successful")
            return {'sync_response': response, 'is_sync': True}
        except ClientError as sync_error:
            # Check if it's a document size error (common reason for sync failure)
            error_code = sync_error.response.get('Error', {}).get('Code', '')
            if error_code in ['InvalidParameterException', 'UnsupportedDocumentException']:
                logger.info(f"Document too large for sync processing ({error_code}), using async")
            else:
                logger.warning(f"Synchronous Textract failed: {sync_error}")
            logger.info("Falling back to asynchronous Textract")
        
        # Asynchronous text detection - only text, no tables or forms for speed
        response = textract_client.start_document_text_detection(
            DocumentLocation={
                'S3Object': {
                    'Bucket': bucket,
                    'Name': key
                }
            }
            # No FeatureTypes specified = text only (fastest and cheapest)
        )
        
        job_id = response['JobId']
        logger.info(f"Textract async text detection job started with ID: {job_id}")
        
        return {'JobId': job_id, 'is_sync': False}
    except ClientError as e:
        logger.error(f"Error starting Textract job: {e}")
        raise


def _check_textract_job_status(textract_client, textract_response: dict, logger) -> dict:
    """Check the status of the Textract job or return sync response."""
    try:
        # Handle synchronous response
        if textract_response.get('is_sync'):
            logger.info("Using synchronous Textract response")
            return textract_response['sync_response']
        
        # Handle asynchronous response
        job_id = textract_response.get('JobId')
        if not job_id:
            raise ValueError("No JobId found in Textract response")
        
        logger.info(f"Monitoring Textract job status for ID: {job_id}")
        while True:
            response = textract_client.get_document_text_detection(JobId=job_id)
            status = response.get('JobStatus')
            logger.info(f"Textract job status: {status}")
            
            if status in ['SUCCEEDED', 'FAILED']:
                logger.info(f"Textract job completed with status: {status}")
                return response
            elif status == 'IN_PROGRESS':
                # Reduced sleep time for faster polling
                time.sleep(5)
            else:
                raise ValueError(f"Unexpected job status: {status}")
    except ClientError as e:
        logger.error(f"Error checking Textract job status: {e}")
        raise


def _convert_textract_to_structured_format(textract_response: dict, logger) -> str:
    """Convert Textract response to structured text format for LLM processing."""
    try:
        blocks = textract_response.get('Blocks', [])
        
        # Extract text blocks with coordinates in the required format
        text_lines = []
        
        # Process LINE blocks for text with coordinates (most efficient)
        for block in blocks:
            if block['BlockType'] == 'LINE':
                bbox = block.get('Geometry', {}).get('BoundingBox', {})
                text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues
                
                # Convert to x1, y1, x2, y2 format
                x1 = bbox.get('Left', 0)
                y1 = bbox.get('Top', 0)
                x2 = x1 + bbox.get('Width', 0)
                y2 = y1 + bbox.get('Height', 0)
                
                text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")
        
        # If no LINE blocks found (sync response), use WORD blocks
        if not text_lines:
            logger.info("No LINE blocks found, using WORD blocks for text extraction")
            for block in blocks:
                if block['BlockType'] == 'WORD':
                    bbox = block.get('Geometry', {}).get('BoundingBox', {})
                    text = block.get('Text', '').replace(',', ';')  # Replace commas to avoid CSV issues
                    
                    # Convert to x1, y1, x2, y2 format
                    x1 = bbox.get('Left', 0)
                    y1 = bbox.get('Top', 0)
                    x2 = x1 + bbox.get('Width', 0)
                    y2 = y1 + bbox.get('Height', 0)
                    
                    text_lines.append(f"{text}, {x1:.4f}, {y1:.4f}, {x2:.4f}, {y2:.4f}")
        
        # Build the structured text format (text only, no tables for speed)
        structured_text_parts = []
        
        # Add header
        structured_text_parts.append("=== TEXT WITH COORDINATES ===")
        structured_text_parts.append("text, x1, y1, x2, y2")
        
        # Add text lines
        for line in text_lines:
            structured_text_parts.append(line)
        
        structured_text = "\n".join(structured_text_parts)
        
        logger.info(f"Extracted {len(text_lines)} text lines from document")
        return structured_text
        
    except Exception as e:
        logger.error(f"Error converting Textract response to structured format: {e}")
        raise


# Example usage
if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Example usage
    file_path = "data/input_data/10k_w_true_data/invoice_shortlisted_v2/SmartWay Transportation/processed/11509907_Invoice.pdf"
    bucket_name = "document-extraction-logistically"
    
    try:
        textract_response, formatted_text = process_file_with_textract(
            file_path=file_path,
            bucket_name=bucket_name,
            logger=logger
        )
        
        print("=== TEXTRACT RESPONSE ===")
        print(json.dumps(textract_response, indent=2, default=str))
        
        print("\n=== FORMATTED TEXT WITH COORDINATES ===")
        print(formatted_text)
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
